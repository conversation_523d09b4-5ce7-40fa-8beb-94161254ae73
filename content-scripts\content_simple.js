// 简化版悬浮球 - 直接点击清理弹窗
(function() {
    'use strict';
    
    // 创建悬浮球
    function createFloatBall() {
        // 检查是否已存在悬浮球
        if (document.getElementById('tm-float-ball')) {
            return;
        }
        
        // 创建悬浮球容器
        const floatBall = document.createElement('div');
        floatBall.id = 'tm-float-ball';
        floatBall.innerHTML = `
            <div class="tm-float-ball-container">
                <div class="tm-float-ball-icon">🧹</div>
                <div class="tm-float-ball-text">清理弹窗</div>
            </div>
        `;
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            #tm-float-ball {
                position: fixed;
                top: 62%;
                right: 5%;
                width: 80px;
                height: 80px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 50%;
                cursor: pointer;
                z-index: 19891001;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
                user-select: none;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
            }
            
            #tm-float-ball:hover {
                transform: scale(1.1);
                box-shadow: 0 6px 25px rgba(0,0,0,0.4);
            }
            
            #tm-float-ball:active {
                transform: scale(0.95);
            }
            
            .tm-float-ball-container {
                text-align: center;
                color: white;
            }
            
            .tm-float-ball-icon {
                font-size: 24px;
                margin-bottom: 2px;
            }
            
            .tm-float-ball-text {
                font-size: 10px;
                font-weight: bold;
                opacity: 0.9;
            }
            
            /* 拖拽时的样式 */
            #tm-float-ball.dragging {
                opacity: 0.8;
                transform: scale(1.05);
            }
        `;
        
        // 添加样式到页面
        document.head.appendChild(style);
        
        // 添加悬浮球到页面
        document.body.appendChild(floatBall);
        
        // 添加拖拽功能
        makeDraggable(floatBall);
        
        // 添加点击事件 - 直接清理弹窗
        floatBall.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            clearPopups();
        });
    }
    
    // 清理弹窗函数
    function clearPopups() {
        try {
            // 显示清理提示
            showNotification('正在清理弹窗...', 'info');
            
            let removedCount = 0;
            
            // 清理各种类型的弹窗和遮罩
            const selectors = [
                '[data-testid=beast-core-modal-mask]',
                '[class*="MDL_mask"]',
                '[data-testid=beast-core-modal]', 
                '[class*="MDL_outerWrapper"]',
                '[class*="MDL_modal"]',
                '.modal-mask',
                '.modal-wrapper',
                '.el-overlay',
                '.ant-modal-mask',
                '.ant-modal-wrap',
                '[class*="Modal"]',
                '[class*="popup"]',
                '[class*="dialog"]'
            ];
            
            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element && element.parentNode) {
                        element.parentNode.removeChild(element);
                        removedCount++;
                    }
                });
            });
            
            // 显示清理结果
            if (removedCount > 0) {
                showNotification(`已清理 ${removedCount} 个弹窗元素`, 'success');
            } else {
                showNotification('未发现需要清理的弹窗', 'info');
            }
            
        } catch (error) {
            console.error('清理弹窗时出错:', error);
            showNotification('清理弹窗时出错', 'error');
        }
    }
    
    // 显示通知
    function showNotification(message, type = 'info') {
        // 移除已存在的通知
        const existingNotification = document.getElementById('tm-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        const notification = document.createElement('div');
        notification.id = 'tm-notification';
        notification.textContent = message;

        const colors = {
            success: '#52c41a',
            error: '#ff4d4f',
            info: '#1890ff',
            warning: '#faad14'
        };

        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: ${colors[type] || colors.info};
            color: white;
            padding: 16px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            z-index: 19891002;
            box-shadow: 0 8px 24px rgba(0,0,0,0.2);
            animation: fadeInScale 0.3s ease;
            text-align: center;
            min-width: 200px;
            max-width: 400px;
        `;

        // 添加动画样式
        if (!document.getElementById('tm-notification-style')) {
            const animationStyle = document.createElement('style');
            animationStyle.id = 'tm-notification-style';
            animationStyle.textContent = `
                @keyframes fadeInScale {
                    from {
                        transform: translate(-50%, -50%) scale(0.8);
                        opacity: 0;
                    }
                    to {
                        transform: translate(-50%, -50%) scale(1);
                        opacity: 1;
                    }
                }
                @keyframes fadeOutScale {
                    from {
                        transform: translate(-50%, -50%) scale(1);
                        opacity: 1;
                    }
                    to {
                        transform: translate(-50%, -50%) scale(0.8);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(animationStyle);
        }

        document.body.appendChild(notification);

        // 0.5秒后自动移除
        setTimeout(() => {
            if (notification && notification.parentNode) {
                notification.style.animation = 'fadeOutScale 0.3s ease';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 500);
    }
    
    // 拖拽功能
    function makeDraggable(element) {
        let isDragging = false;
        let startX, startY, startLeft, startTop;
        
        element.addEventListener('mousedown', function(e) {
            isDragging = true;
            element.classList.add('dragging');
            
            startX = e.clientX;
            startY = e.clientY;
            startLeft = parseInt(window.getComputedStyle(element).right);
            startTop = parseInt(window.getComputedStyle(element).top);
            
            e.preventDefault();
        });
        
        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;
            
            const deltaX = startX - e.clientX;
            const deltaY = e.clientY - startY;
            
            const newRight = startLeft + deltaX;
            const newTop = startTop + deltaY;
            
            // 限制在窗口范围内
            const maxRight = window.innerWidth - element.offsetWidth;
            const maxTop = window.innerHeight - element.offsetHeight;
            
            element.style.right = Math.max(0, Math.min(newRight, maxRight)) + 'px';
            element.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
        });
        
        document.addEventListener('mouseup', function() {
            if (isDragging) {
                isDragging = false;
                element.classList.remove('dragging');
            }
        });
    }
    
    // 监听来自background的消息
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
            if (message.action === 'handFloatBall') {
                if (message.floatBallShow) {
                    createFloatBall();
                } else {
                    const floatBall = document.getElementById('tm-float-ball');
                    if (floatBall) {
                        floatBall.remove();
                    }
                }
            } else if (message.action === 'injectAutoAddDelivery') {
                // 自动清理弹窗
                clearPopups();
            }
        });
    }
    
    // 页面加载完成后创建悬浮球
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', createFloatBall);
    } else {
        createFloatBall();
    }
    
})();
