var background=function(){"use strict";var S,I;function B(a){return a==null||typeof a=="function"?{main:a}:a}const o=(I=(S=globalThis.browser)==null?void 0:S.runtime)!=null&&I.id?globalThis.browser:globalThis.chrome,C=B(()=>{o.webRequest.onBeforeRequest.addListener(function(e){if(e.requestBody&&e.requestBody.raw)for(let n=0;n<e.requestBody.raw.length;n++){let i=e.requestBody.raw[n],s=new TextDecoder("utf-8").decode(i.bytes);try{let r=JSON.parse(s);e.url.includes("queryPopUpWindow")?r.mallId&&o.storage.local.set({mallId:r.mallId}):e.url.includes("querySubOrderList")?o.tabs.query({active:!0,currentWindow:!0},t=>{o.tabs.sendMessage(t[0].id,{action:"injectAutoAddDelivery "})}):e.url.includes("add")&&(console.log(r,"采集"),o.storage.local.get("collectBoxData",t=>{let c=null;r.productId=a(),r.createTime=w(),r.updatedTime=r.createTime,t&&t.collectBoxData&&t.collectBoxData.length>0?(c=t.collectBoxData,c.unshift(r)):c=[r],o.storage.local.set({collectBoxData:c})}))}catch(r){console.error("Error parsing JSON:",r)}}},{urls:["*://*/*/seller/auth/userInfo","*://*/*/product/add","*://*/*/*/puWinCenter/queryPopUpWindow","*://*/*/*/*/*/purchase/manager/querySubOrderList"]},["requestBody"]),o.webRequest.onBeforeSendHeaders.addListener(function(e){if(e.requestHeaders&&e.requestHeaders.length>0){const n=e.requestHeaders.find(i=>i.name=="Anti-Content");n&&n.value&&o.storage.local.set({antiContent:n.value})}},{urls:["<all_urls>"]},["requestHeaders","extraHeaders"]),o.webRequest.onCompleted.addListener(function(e){e.url.includes("querySubOrderList")&&o.tabs.query({active:!0,currentWindow:!0},n=>{o.tabs.sendMessage(n[0].id,{action:"injectAutoAddDelivery"})})},{urls:["*://*/*/*/purchase/manager/querySubOrderList"]},["responseHeaders","extraHeaders"]);function a(){let e=new Date().getTime(),n=Math.floor(Math.random()*1e4);return`${e}-${n}`}function w(){let e=new Date,n=e.getFullYear(),i=String(e.getMonth()+1).padStart(2,"0"),u=String(e.getDate()).padStart(2,"0"),s=String(e.getHours()).padStart(2,"0"),r=String(e.getMinutes()).padStart(2,"0"),t=String(e.getSeconds()).padStart(2,"0");return`${n}-${i}-${u} ${s}:${r}:${t}`}o.runtime.onMessage.addListener(function(e,n){var i;if(e.action==="handleAutoListing")o.tabs.create({url:"https://agentseller.temu.com/goods/create/category"},function(u){x(),o.tabs.onUpdated.addListener(function(s,r){s===u.id&&r.status==="complete"&&v(u,e)})});else if(e.action==="productDraftSave"){const u=(i=n==null?void 0:n.tab)==null?void 0:i.id;o.storage.local.get("mallId",s=>{s&&s.mallId?o.scripting.executeScript({target:{tabId:u},func:(r,t)=>{const c=sessionStorage.getItem("productInfo");try{fetch("https://agentseller.temu.com/visage-agent-seller/product/draft/save",{method:"POST",headers:{"Content-Type":"application/json","Anti-Content":r,"Cache-Control":"max-age=0",Mallid:t},body:c}).then(d=>d.json()).then(()=>{sessionStorage.removeItem("handleListingAuto"),sessionStorage.removeItem("productInfo"),window.postMessage({action:"handleAutoListingRefresh"},"*")}).catch(d=>{console.error("保存草稿接口异常:",d)})}catch(d){console.log("模拟temu卖家后台的保存草稿异常",d)}},args:[e.antiContent,s.mallId]}):console.log("保存前未获取到店铺id")})}else e.action==="handFloatBall"?o.tabs.query({},u=>{u.forEach(s=>{o.tabs.sendMessage(s.id,e)})}):e.action==="saveMallId"&&e.mallId&&o.storage.local.set({mallId:e.mallId})});function v(e,n){o.scripting.executeScript({target:{tabId:e.id},func:(i,u)=>{if(sessionStorage.setItem("handleListingAuto","1"),location.pathname==="/goods/create/category"){const s=JSON.parse(i),r=JSON.parse(u);let t=0;const c=6,d=[];let y=null;for(let f=1;f<=c;f++){const m=s[`cat${f}Id`];if(m==0)break;const h=r[m];if(h)d.push(h);else return new Error("类目id未获取到对应类目名称")}const A=function(){var m,h;const f=document.querySelectorAll('[class^="CSD_menuUl"]');if(f){const O=document.querySelectorAll("[data-testid=beast-core-modal-mask]"),M=document.querySelectorAll("[data-testid=beast-core-modal]");for(const l of O)(m=l==null?void 0:l.parentNode)==null||m.removeChild(l);for(const l of M)(h=l==null?void 0:l.parentNode)==null||h.removeChild(l);if(f[t]&&f[t].children.length>0&&t<c){const l=f[t].children;if(d[t]){for(let g=0;g<l.length;g++){const L=l[g];if(L.innerText===d[t]){L.click();break}}t++,y&&clearTimeout(y),y=setTimeout(()=>{if(t>=d.length){const g=document.querySelector('[data-testid="beast-core-button"]');g&&g.click(),D.disconnect()}},1e3)}return new Error("类目名称未查找到对应类目dom")}}};let D=new MutationObserver(A),N={childList:!0,subtree:!0};D.observe(document.documentElement,N)}else if(location.pathname==="/goods/edit"){const s=new URL(location.href),t=new URLSearchParams(s.search).get("productDraftId"),c=JSON.parse(i);c.productDraftId=t,sessionStorage.setItem("productInfo",JSON.stringify(c))}},args:[JSON.stringify(n.row),JSON.stringify(q)]})}let q;function x(){fetch("https://static-global.zingfront.com/th/static/json/category_cn.json").then(e=>{if(!e.ok)throw new Error("Network response was not ok");return e.json()}).then(e=>{q=e}).catch(e=>{console.error("Fetch error:",e)})}});function k(){}function p(a,...w){}const T={debug:(...a)=>p(console.debug,...a),log:(...a)=>p(console.log,...a),warn:(...a)=>p(console.warn,...a),error:(...a)=>p(console.error,...a)};let b;try{b=C.main(),b instanceof Promise&&console.warn("The background's main() function return a promise, but it must be synchronous")}catch(a){throw T.error("The background crashed on startup!"),a}return b}();
background;
