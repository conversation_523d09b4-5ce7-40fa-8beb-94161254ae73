var background = function() {
    "use strict";

    var S, I;

    function B(a) {
        return a == null || typeof a == "function" ? {main: a} : a;
    }

    const o = (I = (S = globalThis.browser) == null ? void 0 : S.runtime) != null && I.id ? globalThis.browser : globalThis.chrome;

    const C = B(() => {
        // 监听网络请求头 - 获取Anti-Content信息
        o.webRequest.onBeforeSendHeaders.addListener(function(e) {
            if (e.requestHeaders && e.requestHeaders.length > 0) {
                const n = e.requestHeaders.find(i => i.name == "Anti-Content");
                n && n.value && o.storage.local.set({antiContent: n.value});
            }
        }, {urls: ["<all_urls>"]}, ["requestHeaders", "extraHeaders"]);

        // 监听完成的请求 - 只保留自动加入发货台功能
        o.webRequest.onCompleted.addListener(function(e) {
            if (e.url.includes("querySubOrderList")) {
                o.tabs.query({active: true, currentWindow: true}, n => {
                    o.tabs.sendMessage(n[0].id, {action: "injectAutoAddDelivery"});
                });
            }
        }, {urls: ["*://*/*/*/purchase/manager/querySubOrderList"]}, ["responseHeaders", "extraHeaders"]);

        // 监听消息 - 只保留浮动球和店铺ID保存功能
        o.runtime.onMessage.addListener(function(e, n) {
            if (e.action === "handFloatBall") {
                o.tabs.query({}, u => {
                    u.forEach(s => {
                        o.tabs.sendMessage(s.id, e);
                    });
                });
            } else if (e.action === "saveMallId" && e.mallId) {
                o.storage.local.set({mallId: e.mallId});
            }
        });
    });

    function k() {}

    function p(a, ...w) {}

    const T = {
        debug: (...a) => p(console.debug, ...a),
        log: (...a) => p(console.log, ...a),
        warn: (...a) => p(console.warn, ...a),
        error: (...a) => p(console.error, ...a)
    };

    let b;
    try {
        b = C.main();
        b instanceof Promise && console.warn("The background's main() function return a promise, but it must be synchronous");
    } catch (a) {
        throw T.error("The background crashed on startup!"), a;
    }

    return b;
}();
background;
