var utils=function(){"use strict";var i,a;const c=(a=(i=globalThis.browser)==null?void 0:i.runtime)!=null&&a.id?globalThis.browser:globalThis.chrome,u=e=>c.runtime.getURL(e),h=(e="")=>{const r=document.createElement("div");e&&(r.id=e);const n=r.attachShadow({mode:"closed"}),t=document.createElement("div");return t.style.display="none",g(n,t),n.appendChild(t),{wrapper:r,el:t}},g=(e,r)=>{let n=document.createElement("link");n.rel="stylesheet",n.href=c.runtime.getURL("/contentStyle/index.css"),n.onload=()=>{r.style.display=""},e.appendChild(n);const t=document.createElement("style"),l=c.runtime.getURL("/content-scripts/content.css");fetch(l).then(o=>o.text()).then(o=>{const d=o.replace(/:root/g,":host");t.textContent=d,e.appendChild(t)})},m={getChromeRes:u,myAttachShadow:h,matchPattern:(e,r)=>{const n=(t,l)=>{let o=t.replace(/\./g,"\\.");return o=o.replace(/\*/g,".*"),o="^"+o+"$",new RegExp(o).test(l)};if(Array.isArray(e)){for(let t=0;t<e.length;t++)if(n(e[t],r))return!0;return!1}else return n(e,r)}};function f(){}function s(e,...r){}const p={debug:(...e)=>s(console.debug,...e),log:(...e)=>s(console.log,...e),warn:(...e)=>s(console.warn,...e),error:(...e)=>s(console.error,...e)};return(async()=>{try{return await m.main()}catch(e){throw p.error('The unlisted script "utils" crashed on startup!',e),e}})()}();
utils;
